<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// تضمين إعدادات قاعدة البيانات
require_once 'config/database-live.php';

try {
    // الاتصال بقاعدة البيانات
    $pdo = getDatabaseConnection();
    
    // إنشاء الجداول عند الحاجة
    createUserTables($pdo);
    
    // قراءة البيانات المرسلة
    $input_raw = file_get_contents('php://input');
    $input = json_decode($input_raw, true);

    if (json_last_error() !== JSON_ERROR_NONE) {
        $input = $_POST;
    }

    $action = $input['action'] ?? $_GET['action'] ?? '';

    if (empty($action)) {
        throw new Exception('لم يتم تحديد الإجراء المطلوب');
    }

    switch ($action) {
        case 'test':
            testUserManagementAPI($pdo, $input);
            break;
            
        case 'load_all_users':
            loadAllUsersAPI($pdo, $input);
            break;
            
        case 'save_user':
            saveUserAPI($pdo, $input);
            break;
            
        case 'delete_user':
            deleteUserAPI($pdo, $input);
            break;
            
        case 'authenticate_user':
            authenticateUserAPI($pdo, $input);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح: ' . $action);
    }
    
} catch (PDOException $e) {
    http_response_code(500);
    error_log('خطأ في قاعدة البيانات user-management-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في قاعدة البيانات: ' . $e->getMessage(),
        'error_type' => 'database',
        'file' => 'user-management-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
} catch (Exception $e) {
    http_response_code(500);
    error_log('خطأ في user-management-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => 'خطأ في User Management API: ' . $e->getMessage(),
        'error_type' => 'general',
        'file' => 'user-management-api.php',
        'timestamp' => date('Y-m-d H:i:s')
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * إنشاء جداول المستخدمين
 */
function createUserTables($pdo) {
    // جدول المستخدمين
    $sql = "
    CREATE TABLE IF NOT EXISTS users (
        id VARCHAR(50) PRIMARY KEY,
        username VARCHAR(100) NOT NULL UNIQUE,
        password VARCHAR(255) NOT NULL,
        name VARCHAR(200) NOT NULL,
        center VARCHAR(200),
        region VARCHAR(200),
        center_id VARCHAR(50),
        role VARCHAR(50) DEFAULT 'nurse',
        accountType VARCHAR(50) DEFAULT 'nurse',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        last_login TIMESTAMP NULL,
        is_active BOOLEAN DEFAULT TRUE,
        INDEX idx_username (username),
        INDEX idx_center_id (center_id),
        INDEX idx_role (role)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
    ";

    $pdo->exec($sql);

    // إضافة الأعمدة المفقودة إذا لم تكن موجودة
    try {
        // فحص وإضافة عمود center
        $pdo->exec("ALTER TABLE users ADD COLUMN center VARCHAR(200) DEFAULT NULL");
    } catch (PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        // فحص وإضافة عمود region
        $pdo->exec("ALTER TABLE users ADD COLUMN region VARCHAR(200) DEFAULT NULL");
    } catch (PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        // فحص وإضافة عمود center_id
        $pdo->exec("ALTER TABLE users ADD COLUMN center_id VARCHAR(50) DEFAULT NULL");
    } catch (PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        // فحص وإضافة عمود role
        $pdo->exec("ALTER TABLE users ADD COLUMN role VARCHAR(50) DEFAULT 'nurse'");
    } catch (PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        // فحص وإضافة عمود accountType
        $pdo->exec("ALTER TABLE users ADD COLUMN accountType VARCHAR(50) DEFAULT 'nurse'");
    } catch (PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        // فحص وإضافة عمود last_login
        $pdo->exec("ALTER TABLE users ADD COLUMN last_login TIMESTAMP NULL");
    } catch (PDOException $e) {
        // العمود موجود بالفعل
    }

    try {
        // فحص وإضافة عمود is_active
        $pdo->exec("ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT TRUE");
    } catch (PDOException $e) {
        // العمود موجود بالفعل
    }
}

/**
 * اختبار API
 */
function testUserManagementAPI($pdo, $input) {
    echo json_encode([
        'success' => true,
        'message' => 'User Management API يعمل بنجاح',
        'timestamp' => date('Y-m-d H:i:s'),
        'version' => '1.0'
    ], JSON_UNESCAPED_UNICODE);
}

/**
 * تحميل جميع المستخدمين
 */
function loadAllUsersAPI($pdo, $input) {
    try {
        $stmt = $pdo->prepare("
            SELECT id, username, name, center, region, center_id, role, accountType, 
                   created_at, last_login, is_active 
            FROM users 
            WHERE is_active = TRUE 
            ORDER BY name
        ");
        $stmt->execute();
        $users = $stmt->fetchAll(PDO::FETCH_ASSOC);

        echo json_encode([
            'success' => true,
            'users' => $users,
            'count' => count($users),
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        throw new Exception('فشل في تحميل المستخدمين: ' . $e->getMessage());
    }
}

/**
 * حفظ مستخدم
 */
function saveUserAPI($pdo, $input) {
    $id = $input['id'] ?? '';
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';
    $name = $input['name'] ?? '';
    $center = $input['center'] ?? '';
    $region = $input['region'] ?? '';
    $center_id = $input['center_id'] ?? '';
    $role = $input['role'] ?? 'nurse';
    $accountType = $input['accountType'] ?? 'nurse';

    if (empty($username) || empty($name)) {
        throw new Exception('البيانات المطلوبة مفقودة: username, name');
    }

    try {
        if (empty($id)) {
            // إنشاء مستخدم جديد
            $id = 'nurse_' . uniqid();
            $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
            
            $stmt = $pdo->prepare("
                INSERT INTO users (id, username, password, name, center, region, center_id, role, accountType) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$id, $username, $hashedPassword, $name, $center, $region, $center_id, $role, $accountType]);
        } else {
            // تحديث مستخدم موجود
            if (!empty($password)) {
                // تحديث مع كلمة مرور جديدة
                $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET username = ?, password = ?, name = ?, center = ?, region = ?, 
                        center_id = ?, role = ?, accountType = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([$username, $hashedPassword, $name, $center, $region, $center_id, $role, $accountType, $id]);
            } else {
                // تحديث بدون تغيير كلمة المرور
                $stmt = $pdo->prepare("
                    UPDATE users 
                    SET username = ?, name = ?, center = ?, region = ?, 
                        center_id = ?, role = ?, accountType = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ");
                $stmt->execute([$username, $name, $center, $region, $center_id, $role, $accountType, $id]);
            }
        }

        echo json_encode([
            'success' => true,
            'message' => empty($input['id']) ? 'تم إنشاء المستخدم بنجاح' : 'تم تحديث المستخدم بنجاح',
            'user_id' => $id,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        throw new Exception('فشل في حفظ المستخدم: ' . $e->getMessage());
    }
}

/**
 * حذف مستخدم
 */
function deleteUserAPI($pdo, $input) {
    $user_id = $input['user_id'] ?? '';

    if (empty($user_id)) {
        throw new Exception('معرف المستخدم مطلوب');
    }

    try {
        // حذف ناعم - تعطيل المستخدم بدلاً من حذفه
        $stmt = $pdo->prepare("UPDATE users SET is_active = FALSE, updated_at = CURRENT_TIMESTAMP WHERE id = ?");
        $stmt->execute([$user_id]);

        echo json_encode([
            'success' => true,
            'message' => 'تم حذف المستخدم بنجاح',
            'user_id' => $user_id,
            'timestamp' => date('Y-m-d H:i:s')
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        throw new Exception('فشل في حذف المستخدم: ' . $e->getMessage());
    }
}

/**
 * تسجيل دخول المستخدم
 */
function authenticateUserAPI($pdo, $input) {
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';

    if (empty($username) || empty($password)) {
        throw new Exception('اسم المستخدم وكلمة المرور مطلوبان');
    }

    try {
        $stmt = $pdo->prepare("
            SELECT id, username, password, name, center, region, center_id, role, accountType 
            FROM users 
            WHERE username = ? AND is_active = TRUE
        ");
        $stmt->execute([$username]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($user && password_verify($password, $user['password'])) {
            // تحديث وقت آخر دخول
            $updateStmt = $pdo->prepare("UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?");
            $updateStmt->execute([$user['id']]);

            // إزالة كلمة المرور من الاستجابة
            unset($user['password']);

            echo json_encode([
                'success' => true,
                'message' => 'تم تسجيل الدخول بنجاح',
                'user' => $user,
                'timestamp' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة',
                'timestamp' => date('Y-m-d H:i:s')
            ], JSON_UNESCAPED_UNICODE);
        }

    } catch (Exception $e) {
        throw new Exception('فشل في تسجيل الدخول: ' . $e->getMessage());
    }
}
?>
