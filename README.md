# CS Manager - نظام إدارة المراكز الصحية

نظام شامل لإدارة المراكز الصحية يشمل إدارة التلقيح، الأدوية، تنظيم الأسرة، وسجل الأطفال.

## 🚀 الميزات الرئيسية

- **إدارة التلقيح**: تتبع جداول التلقيح وحساب المواعيد
- **إدارة الأدوية**: تتبع المخزون والصلاحية
- **تنظيم الأسرة**: إدارة خدمات تنظيم الأسرة
- **سجل الأطفال**: قاعدة بيانات شاملة للأطفال
- **إدارة المهام**: نظام مهام متقدم
- **الرسائل**: نظام تواصل داخلي
- **الإشعارات**: تنبيهات ذكية
- **تصميم متجاوب**: يعمل على جميع الأجهزة

## 📁 هيكل المشروع

```
cs-manager/
├── api/                    # ملفات API
├── assets/                 # الأصول الثابتة
├── config/                 # إعدادات قاعدة البيانات
├── docs/                   # الوثائق
├── js/                     # ملفات JavaScript
├── cs-manager.html         # الملف الرئيسي
├── index.php              # صفحة البداية
└── install.php            # ملف التثبيت
```

## 🛠️ التثبيت

1. ارفع الملفات إلى الخادم
2. قم بتشغيل `install.php` لإعداد قاعدة البيانات
3. اضبط إعدادات قاعدة البيانات في `config/database-live.php`
4. افتح `cs-manager.html` للبدء

## 💻 المتطلبات

- PHP 7.4+
- MySQL 5.7+
- خادم ويب (Apache/Nginx)

## 📱 التوافق

- جميع المتصفحات الحديثة
- الهواتف والأجهزة اللوحية
- أجهزة سطح المكتب

## 🔧 التطوير

تم تطوير النظام باستخدام:
- HTML5, CSS3, JavaScript
- PHP
- MySQL
- Bootstrap Icons
- Font Awesome

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.
