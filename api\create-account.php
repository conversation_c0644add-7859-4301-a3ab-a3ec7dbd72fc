<?php
/**
 * إنشاء حساب جديد في نظام إدارة المراكز الصحية
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // قراءة البيانات
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $name = trim($input['name'] ?? '');
    $user_username = trim($input['username'] ?? '');
    $user_password = trim($input['password'] ?? '');
    $center = trim($input['center'] ?? '');
    $region = trim($input['region'] ?? '');
    $input_role = trim($input['role'] ?? 'nurse');

    // تحويل الأدوار لتتوافق مع قاعدة البيانات
    $role_mapping = [
        'nurse' => 'nurse',
        'head_nurse' => 'supervisor',  // ممرض رئيسي = ممرض رئيسي (supervisor في قاعدة البيانات)
        'supervisor' => 'supervisor',
        'admin' => 'admin'
    ];

    $role = $role_mapping[$input_role] ?? 'nurse';
    
    // التحقق من البيانات المطلوبة
    if (empty($name) || empty($user_username) || empty($user_password) || empty($center) || empty($region)) {
        throw new Exception('يرجى ملء جميع الحقول المطلوبة');
    }
    
    // التحقق من طول كلمة المرور
    if (strlen($user_password) < 6) {
        throw new Exception('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }
    
    // التحقق من وجود اسم المستخدم
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ?");
    $stmt->execute([$user_username]);
    if ($stmt->fetch()) {
        throw new Exception('اسم المستخدم موجود مسبقاً، يرجى اختيار اسم آخر');
    }
    
    // البحث عن المركز أو إنشاؤه
    $stmt = $pdo->prepare("SELECT id FROM centers WHERE name = ?");
    $stmt->execute([$center]);
    $center_data = $stmt->fetch();

    if (!$center_data) {
        // إنشاء مركز جديد
        $stmt = $pdo->prepare("
            INSERT INTO centers (name, location)
            VALUES (?, ?)
        ");
        $stmt->execute([$center, $region]);
        $center_id = $pdo->lastInsertId();
    } else {
        $center_id = $center_data['id'];
    }
    
    // تشفير كلمة المرور
    $hashed_password = password_hash($user_password, PASSWORD_DEFAULT);
    
    // إنشاء معرف فريد للمستخدم
    $user_id = $role . '_' . uniqid();
    
    // إدراج المستخدم الجديد
    $stmt = $pdo->prepare("
        INSERT INTO users (id, username, password, name, role, center_id, is_active, created_at) 
        VALUES (?, ?, ?, ?, ?, ?, 1, CURRENT_TIMESTAMP)
    ");
    
    $stmt->execute([
        $user_id,
        $user_username,
        $hashed_password,
        $name,
        $role,
        (int)$center_id  // تأكد من أن center_id رقم صحيح
    ]);
    
    // إعداد الاستجابة
    echo json_encode([
        'success' => true,
        'message' => 'تم إنشاء الحساب بنجاح',
        'user' => [
            'id' => $user_id,
            'username' => $user_username,
            'name' => $name,
            'role' => $role,
            'original_role' => $input_role,  // الدور الأصلي المرسل
            'center' => $center,
            'region' => $region
        ]
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
