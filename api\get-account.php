<?php
/**
 * جلب بيانات حساب مستخدم واحد
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    // الاتصال بقاعدة البيانات
    $host = '127.0.0.1';
    $dbname = 'csdb';
    $username = 'csdbuser';
    $password = 'j5aKN6lz5bsujTcWaYAd';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $pdo = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    // قراءة البيانات
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $account_id = $input['account_id'] ?? null;
    
    if (!$account_id) {
        throw new Exception('معرف الحساب مطلوب');
    }
    
    // جلب بيانات المستخدم
    $stmt = $pdo->prepare("
        SELECT u.*, c.name as center_name, c.location as center_location
        FROM users u 
        LEFT JOIN centers c ON u.center_id = c.id 
        WHERE u.id = ? AND u.is_active = 1
        LIMIT 1
    ");
    
    $stmt->execute([$account_id]);
    $account = $stmt->fetch();
    
    if (!$account) {
        throw new Exception('الحساب غير موجود');
    }
    
    // تحويل البيانات للتوافق مع الواجهة
    $formatted_account = [
        'id' => $account['id'],
        'username' => $account['username'],
        'name' => $account['name'],
        'role' => $account['role'],
        'center' => $account['center_name'],
        'center_id' => $account['center_id'],
        'center_location' => $account['center_location'],
        'created_at' => $account['created_at'],
        'updated_at' => $account['updated_at']
    ];
    
    echo json_encode([
        'success' => true,
        'account' => $formatted_account
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
