<?php
/**
 * API إدارة المهام
 */

session_start();
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

// تضمين إعدادات قاعدة البيانات
require_once 'config/database-live.php';

try {
    // الاتصال بقاعدة البيانات باستخدام الإعدادات المناسبة
    $pdo = getDatabaseConnection();
    
    // إنشاء جدول المهام إذا لم يكن موجوداً
    createTasksTable($pdo);
    
    // قراءة البيانات
    $input = json_decode(file_get_contents('php://input'), true);
    if (!$input) {
        $input = $_POST;
    }
    
    $action = $input['action'] ?? $_GET['action'] ?? '';
    
    switch ($action) {
        case 'load':
            loadTasks($pdo, $input);
            break;
            
        case 'save':
            saveTask($pdo, $input);
            break;
            
        case 'update':
            updateTask($pdo, $input);
            break;
            
        case 'delete':
            deleteTask($pdo, $input);
            break;
            
        case 'toggle_completion':
            toggleTaskCompletion($pdo, $input);
            break;
            
        default:
            throw new Exception('إجراء غير صحيح');
    }
    
} catch (Exception $e) {
    http_response_code(400);
    error_log('خطأ في tasks-api.php: ' . $e->getMessage());
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage(),
        'file' => 'tasks-api.php'
    ], JSON_UNESCAPED_UNICODE);
}

// إنشاء جدول المهام
function createTasksTable($pdo) {
    try {
        $stmt = $pdo->query("SHOW TABLES LIKE 'tasks'");
        if ($stmt->rowCount() > 0) {
            return; // الجدول موجود
        }
        
        $sql = "
        CREATE TABLE tasks (
            id VARCHAR(50) PRIMARY KEY,
            user_id VARCHAR(50) NOT NULL,
            center_id VARCHAR(50) NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT,
            due_date DATE,
            priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
            completed BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_user_id (user_id),
            INDEX idx_center_id (center_id),
            INDEX idx_due_date (due_date),
            INDEX idx_completed (completed)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;
        ";
        
        $pdo->exec($sql);
        
    } catch (Exception $e) {
        error_log('خطأ في إنشاء جدول المهام: ' . $e->getMessage());
    }
}

// تحميل المهام
function loadTasks($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    
    if (!$user_id) {
        throw new Exception('معرف المستخدم مطلوب');
    }
    
    try {
        // فحص إذا كان عمود user_id موجود
        $stmt = $pdo->query("SHOW COLUMNS FROM tasks LIKE 'user_id'");
        $userIdExists = $stmt->rowCount() > 0;

        if ($userIdExists) {
            $stmt = $pdo->prepare("
                SELECT * FROM tasks
                WHERE user_id = ?
                ORDER BY
                    CASE
                        WHEN due_date IS NULL THEN 1
                        ELSE 0
                    END,
                    due_date ASC,
                    created_at DESC
            ");
            $stmt->execute([$user_id]);
        } else {
            // إذا لم يكن user_id موجود، جلب جميع المهام
            $stmt = $pdo->query("
                SELECT * FROM tasks
                ORDER BY
                    CASE
                        WHEN due_date IS NULL THEN 1
                        ELSE 0
                    END,
                    due_date ASC,
                    created_at DESC
            ");
        }

        $tasks = $stmt->fetchAll();
        
        // تحويل البيانات للتوافق مع الواجهة
        $formatted_tasks = [];
        foreach ($tasks as $task) {
            // تحديد حالة الإكمال
            $completed = false;
            if (isset($task['completed'])) {
                $completed = (bool)$task['completed'];
            } elseif (isset($task['status'])) {
                $completed = ($task['status'] === 'completed');
            }

            $formatted_tasks[] = [
                'id' => $task['id'],
                'title' => $task['title'],
                'description' => $task['description'] ?? '',
                'dueDate' => $task['due_date'] ?? null,
                'priority' => $task['priority'] ?? 'medium',
                'completed' => $completed,
                'status' => $task['status'] ?? ($completed ? 'completed' : 'pending'),
                'createdAt' => $task['created_at'] ?? null,
                'updatedAt' => $task['updated_at'] ?? null
            ];
        }
        
        echo json_encode([
            'success' => true,
            'tasks' => $formatted_tasks
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            echo json_encode([
                'success' => true,
                'tasks' => [],
                'message' => 'جدول المهام غير موجود - سيتم إنشاؤه عند إضافة أول مهمة'
            ], JSON_UNESCAPED_UNICODE);
            return;
        }
        throw $e;
    }
}

// حفظ مهمة جديدة
function saveTask($pdo, $input) {
    $user_id = $input['user_id'] ?? '';
    $center_id = $input['center_id'] ?? '';
    $title = $input['title'] ?? '';
    $description = $input['description'] ?? '';
    $due_date = $input['due_date'] ?? null;
    $priority = $input['priority'] ?? 'medium';

    if (!$user_id || !$title) {
        throw new Exception('البيانات الأساسية مطلوبة: user_id, title');
    }

    // تحويل تاريخ الاستحقاق إلى صيغة MySQL إذا لزم الأمر
    if ($due_date && strpos($due_date, '/') !== false) {
        $parts = explode('/', $due_date);
        if (count($parts) === 3) {
            $due_date = $parts[2] . '-' . str_pad($parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($parts[0], 2, '0', STR_PAD_LEFT);
        }
    }

    $task_id = $input['id'] ?? (time() . '_' . uniqid());

    try {
        createTasksTable($pdo); // التأكد من وجود الجدول

        // فحص الأعمدة الموجودة
        $stmt = $pdo->query("SHOW COLUMNS FROM tasks");
        $columns = $stmt->fetchAll();
        $columnNames = array_column($columns, 'Field');

        // فحص نوع عمود id
        $idColumn = array_filter($columns, function($col) {
            return $col['Field'] === 'id';
        });
        $idType = !empty($idColumn) ? reset($idColumn)['Type'] : '';
        $isAutoIncrement = !empty($idColumn) ? (strpos(reset($idColumn)['Extra'], 'auto_increment') !== false) : false;

        $insertColumns = ['title'];
        $insertValues = [$title];
        $placeholders = ['?'];

        // إضافة id فقط إذا لم يكن auto_increment
        if (!$isAutoIncrement) {
            $insertColumns[] = 'id';
            $insertValues[] = $task_id;
            $placeholders[] = '?';
        }

        // إضافة الأعمدة الموجودة
        if (in_array('user_id', $columnNames)) {
            $insertColumns[] = 'user_id';
            $insertValues[] = $user_id;
            $placeholders[] = '?';
        }

        if (in_array('center_id', $columnNames) && $center_id) {
            $insertColumns[] = 'center_id';
            $insertValues[] = $center_id;
            $placeholders[] = '?';
        }

        if (in_array('description', $columnNames) && $description) {
            $insertColumns[] = 'description';
            $insertValues[] = $description;
            $placeholders[] = '?';
        }

        if (in_array('due_date', $columnNames) && $due_date) {
            $insertColumns[] = 'due_date';
            $insertValues[] = $due_date;
            $placeholders[] = '?';
        }

        if (in_array('priority', $columnNames)) {
            $insertColumns[] = 'priority';
            $insertValues[] = $priority;
            $placeholders[] = '?';
        }

        $sql = "INSERT INTO tasks (" . implode(', ', $insertColumns) . ") VALUES (" . implode(', ', $placeholders) . ")";
        $stmt = $pdo->prepare($sql);
        $stmt->execute($insertValues);

        // الحصول على ID المهمة
        $final_task_id = $isAutoIncrement ? $pdo->lastInsertId() : $task_id;

        echo json_encode([
            'success' => true,
            'message' => 'تم حفظ المهمة بنجاح',
            'task_id' => $final_task_id
        ], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            createTasksTable($pdo);
            // إعادة المحاولة
            saveTask($pdo, $input);
        } else {
            throw $e;
        }
    }
}

// تحديث مهمة
function updateTask($pdo, $input) {
    $task_id = $input['task_id'] ?? '';
    $title = $input['title'] ?? '';
    $description = $input['description'] ?? '';
    $due_date = $input['due_date'] ?? null;
    $priority = $input['priority'] ?? 'medium';
    
    if (!$task_id || !$title) {
        throw new Exception('معرف المهمة والعنوان مطلوبان');
    }
    
    // تحويل تاريخ الاستحقاق إلى صيغة MySQL إذا لزم الأمر
    if ($due_date && strpos($due_date, '/') !== false) {
        $parts = explode('/', $due_date);
        if (count($parts) === 3) {
            $due_date = $parts[2] . '-' . str_pad($parts[1], 2, '0', STR_PAD_LEFT) . '-' . str_pad($parts[0], 2, '0', STR_PAD_LEFT);
        }
    }
    
    $stmt = $pdo->prepare("
        UPDATE tasks 
        SET title = ?, description = ?, due_date = ?, priority = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
    ");
    
    $stmt->execute([$title, $description, $due_date, $priority, $task_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم تحديث المهمة بنجاح'
    ], JSON_UNESCAPED_UNICODE);
}

// حذف مهمة
function deleteTask($pdo, $input) {
    $task_id = $input['task_id'] ?? '';
    
    if (!$task_id) {
        throw new Exception('معرف المهمة مطلوب');
    }
    
    $stmt = $pdo->prepare("DELETE FROM tasks WHERE id = ?");
    $stmt->execute([$task_id]);
    
    echo json_encode([
        'success' => true,
        'message' => 'تم حذف المهمة بنجاح'
    ], JSON_UNESCAPED_UNICODE);
}

// تبديل حالة إكمال المهمة
function toggleTaskCompletion($pdo, $input) {
    $task_id = $input['task_id'] ?? '';
    $completed = $input['completed'] ?? false;

    if (!$task_id) {
        throw new Exception('معرف المهمة مطلوب');
    }

    // فحص إذا كان الجدول يحتوي على عمود completed أو status
    $stmt = $pdo->query("SHOW COLUMNS FROM tasks");
    $columns = $stmt->fetchAll();
    $columnNames = array_column($columns, 'Field');

    if (in_array('completed', $columnNames)) {
        // استخدام عمود completed
        $stmt = $pdo->prepare("
            UPDATE tasks
            SET completed = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        $stmt->execute([$completed ? 1 : 0, $task_id]);
    } elseif (in_array('status', $columnNames)) {
        // استخدام عمود status
        $status = $completed ? 'completed' : 'pending';
        $stmt = $pdo->prepare("
            UPDATE tasks
            SET status = ?, updated_at = CURRENT_TIMESTAMP
            WHERE id = ?
        ");
        $stmt->execute([$status, $task_id]);
    } else {
        throw new Exception('لا يوجد عمود مناسب لحالة المهمة');
    }

    echo json_encode([
        'success' => true,
        'message' => $completed ? 'تم تحديد المهمة كمكتملة' : 'تم تحديد المهمة كغير مكتملة'
    ], JSON_UNESCAPED_UNICODE);
}
?>
