# CS Manager - Git Ignore File

# Database configuration files (sensitive)
config/database-live.php
config/database-local.php

# Log files
*.log
logs/
error.log
access.log

# Temporary files
*.tmp
*.temp
temp/
tmp/

# Backup files
*.bak
*.backup
*.sql.bak

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# Composer
vendor/
composer.lock

# Node modules (if any)
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Upload directories
uploads/
files/
attachments/

# Cache directories
cache/
*.cache

# Environment files
.env
.env.local
.env.production

# Test files
test-*.html
test-*.php
debug-*.html
debug-*.php

# Documentation build
docs/build/

# Compiled assets
dist/
build/

# Session files
sessions/

# Error pages
error-pages/

# Maintenance mode
maintenance.html
